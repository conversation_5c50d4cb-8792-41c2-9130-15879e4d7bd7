package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.ProjectPage;
import lombok.Data;

/**
 * Root wrapper for the project details endpoint. The JSON structure looks like:
 * {
 *   "pages/project/project_page": { ... }
 * }
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectPageResponse {

    @JsonProperty("pages/project/project_page")
    private ProjectPage projectPage;
}

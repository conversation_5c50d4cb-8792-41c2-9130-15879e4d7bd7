package com.realmond.temporal_service.crawler.uae.alnair.site;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.rate_limit.BucketRateLimiter;
import com.realmond.temporal_service.crawler.uae.alnair.AlnairCommon;
import com.realmond.temporal_service.crawler.uae.alnair.AlnairSettings;
import feign.RequestInterceptor;
import feign.Retryer;
import feign.codec.Decoder;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Random;

/**
 * Shared Feign configuration for the alnair.ae front-end (packed JSON) client.
 */
@Slf4j
@Configuration
public class AlnairSiteFeignConfiguration {

    private final FingerprintGenerator fingerprintGenerator;
    private final AlnairSettings settings;
    private final BucketRateLimiter rateLimiter;
    private final ObjectMapper objectMapper;
    private final Random random = new Random();

    public AlnairSiteFeignConfiguration(FingerprintGenerator fingerprintGenerator,
                                         AlnairSettings settings,
                                         ObjectMapper objectMapper,
                                         ObjectProvider<ProxyManager<String>> proxyManagerProvider) {
        this.fingerprintGenerator = fingerprintGenerator;
        this.settings = settings;
        this.objectMapper = objectMapper;
        ProxyManager<String> proxyManager = proxyManagerProvider.getIfAvailable();
        this.rateLimiter = new BucketRateLimiter(settings.getRateLimiter(), proxyManager);
    }

    @Bean
    public Decoder alnairPackedJsonDecoder() {
        return new AlnairPackedJsonDecoder(objectMapper);
    }

    @Bean
    public RequestInterceptor alnairSiteRequestInterceptor() {
        return template -> {
            if (rateLimiter != null) {
                rateLimiter.consume(AlnairCommon.SOURCE_URN);
            }
            fingerprintGenerator.applyBrowserHeaders(template);
            addRandomDelay();
        };
    }

    @Bean
    public Retryer alnairSiteRetryer() {
        return new Retryer.Default(
                settings.getRetry().getRetryDelayMs(),
                settings.getRetry().getMaxPeriodMs(),
                settings.getRetry().getMaxRetries());
    }

    private void addRandomDelay() {
        try {
            int delay = random.nextInt(settings.getRetry().getRandomDelayCapMs());
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Request delay interrupted", e);
        }
    }
} 
package com.realmond.temporal_service.crawler.uae.alnair.api;

import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.rate_limit.BucketRateLimiter;
import com.realmond.temporal_service.crawler.uae.alnair.AlnairCommon;
import com.realmond.temporal_service.crawler.uae.alnair.AlnairSettings;
import com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictionaryService;
import com.realmond.temporal_service.crawler.uae.alnair.model.Dictionary;
import feign.RequestInterceptor;
import feign.Retryer;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Random;

/**
 * Shared Feign configuration for the api.alnair.ae HTTP client.
 */
@Slf4j
@Configuration
public class AlnairApiFeignConfiguration {

    private final FingerprintGenerator fingerprintGenerator;
    private final AlnairSettings settings;
    private final BucketRateLimiter rateLimiter;
    private final Random random = new Random();
    private final Integer cityId;

    public AlnairApiFeignConfiguration(FingerprintGenerator fingerprintGenerator,
                                        AlnairSettings settings,
                                        AlnairDictionaryService dictionaryService,
                                        ObjectProvider<ProxyManager<String>> proxyManagerProvider) {
        this.fingerprintGenerator = fingerprintGenerator;
        this.settings = settings;
        ProxyManager<String> proxyManager = proxyManagerProvider.getIfAvailable();
        this.rateLimiter = new BucketRateLimiter(settings.getRateLimiter(), proxyManager);
        this.cityId = resolveDubaiCityId(dictionaryService);
    }

    private Integer resolveDubaiCityId(AlnairDictionaryService dictionaryService) {
        try {
            Dictionary.Info info = dictionaryService.getReferenceData();
            if (info != null && info.getCities() != null) {
                return info.getCities()
                        .stream()
                        .filter(c -> "Dubai".equalsIgnoreCase(c.getName()))
                        .map(Dictionary.City::getId)
                        .findFirst()
                        .orElse(1); // fallback
            }
        } catch (Exception e) {
            // ignore and fallback
        }
        return 1;
    }

    @Bean
    public RequestInterceptor alnairApiRequestInterceptor() {
        return template -> {
            if (rateLimiter != null) {
                rateLimiter.consume(AlnairCommon.SOURCE_URN);
            }
            template.header("X-CITY", String.valueOf(cityId));
            fingerprintGenerator.applyBrowserHeaders(template);
            addRandomDelay();
        };
    }

    @Bean
    public Retryer alnairApiRetryer() {
        return new Retryer.Default(
                settings.getRetry().getRetryDelayMs(),
                settings.getRetry().getMaxPeriodMs(),
                settings.getRetry().getMaxRetries());
    }

    private void addRandomDelay() {
        try {
            int delay = random.nextInt(settings.getRetry().getRandomDelayCapMs());
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Request delay interrupted", e);
        }
    }
} 
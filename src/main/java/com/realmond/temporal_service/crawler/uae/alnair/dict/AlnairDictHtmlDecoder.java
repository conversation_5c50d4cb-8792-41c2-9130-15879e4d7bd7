package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import feign.Response;
import feign.codec.Decoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * Feign {@link Decoder} stub that will decode the packed JSON dictionary
 * embedded in Alnair's HTML pages. The full implementation will be completed
 * in later backlog tasks. At this stage the class only needs to compile and
 * therefore returns {@code null} for all responses.
 */
@Slf4j
@RequiredArgsConstructor
public class AlnairDictHtmlDecoder implements Decoder {

    private final ObjectMapper objectMapper;

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        if (response == null || response.body() == null) {
            return null;
        }

        // Read full HTML response body
        String html;
        try (java.io.InputStream is = response.body().asInputStream()) {
            html = new String(is.readAllBytes(), java.nio.charset.StandardCharsets.UTF_8);
        }

        // Parse HTML and locate the <script> element containing the packed JSON
        org.jsoup.nodes.Document document = org.jsoup.Jsoup.parse(html);
        org.jsoup.nodes.Element scriptElement = document
                .select("script")
                .stream()
                .filter(e -> {
                    String data = e.data();
                    if (data == null || data.isBlank()) {
                        data = e.html();
                    }
                    return data.contains("streamController.enqueue(");
                })
                .findFirst()
                .orElse(null);

        if (scriptElement == null) {
            log.warn("Stream controller script not found in HTML – cannot extract dictionary");
            throw FeignException.errorStatus("Missing dictionary", response);
        }

        String scriptContent = scriptElement.data();
        if (scriptContent == null || scriptContent.isBlank()) {
            scriptContent = scriptElement.html();
        }

        // -----------------------------------------------------------------
        // Task 6: Extract the packed JSON string via regex and unescape it.
        // -----------------------------------------------------------------
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("streamController\\.enqueue\\(\"(.*?)\"\\)", java.util.regex.Pattern.DOTALL);
        java.util.regex.Matcher matcher = pattern.matcher(scriptContent);
        if (!matcher.find()) {
            log.error("Packed JSON not found in streamController.enqueue(...) call");
            throw FeignException.errorStatus("Packed JSON not found", response);
        }

        String rawJsonEscaped = matcher.group(1);

        // Unescape the JSON string using Jackson so that sequences like \" etc. are converted
        String packedJson;
        try {
            packedJson = objectMapper.readValue("\"" + rawJsonEscaped + "\"", String.class);
        } catch (IOException e) {
            log.error("Failed to unescape packed JSON string", e);
            throw FeignException.errorStatus("Packed JSON unescape error", response);
        }

        if (isStringReturnType(type)) {
            return packedJson;
        }

        // -----------------------------------------------------------------
        // Task 6.5: Decode packed JSON and extract catalogs
        // -----------------------------------------------------------------
        java.util.List<Object> packedList;
        try {
            packedList = objectMapper.readValue(packedJson, java.util.List.class);
        } catch (IOException e) {
            log.error("Failed to parse packed JSON to List", e);
            throw FeignException.errorStatus("Packed JSON parse error", response);
        }

        java.util.Map<String, Object> decodedMap;
        try {
            decodedMap = new com.realmond.temporal_service.crawler.uae.alnair.decoder.PackedJsonDecoder(packedList).decodeToMap();
        } catch (Exception e) {
            log.error("Failed to decode packed JSON", e);
            throw FeignException.errorStatus("Packed JSON decode error", response);
        }

        com.fasterxml.jackson.databind.JavaType javaType = objectMapper.getTypeFactory().constructType(type);

        try {
            return objectMapper.convertValue(decodedMap, javaType);
        } catch (IllegalArgumentException ex) {
            log.error("Failed to convert decoded JSON to {}", javaType, ex);
            throw FeignException.errorStatus("Dictionary conversion error", response);
        }
    }

    @SuppressWarnings("unchecked")
    private Object navigateToCatalogs(java.util.Map<String, Object> map) {
        Object loaderData = map.get("loaderData");
        if (!(loaderData instanceof java.util.Map<?, ?> ld)) {
            return null;
        }
        Object appLayout = ld.get("routes/layouts/app_layout");
        if (!(appLayout instanceof java.util.Map<?, ?> al)) {
            return null;
        }
        Object info = ((java.util.Map<?, ?>) al).get("info");
        if (!(info instanceof java.util.Map<?, ?> inf)) {
            return null;
        }
        return ((java.util.Map<?, ?>) inf).get("catalogs");
    }

    /**
     * Checks whether the requested return {@code type} is assignable to
     * {@link String}. Handles both raw {@link Class} references and generic
     * {@link java.lang.reflect.ParameterizedType}s.
     */
    private boolean isStringReturnType(Type type) {
        if (type instanceof Class<?>) {
            return String.class.isAssignableFrom((Class<?>) type);
        }
        return "java.lang.String".equals(type.getTypeName());
    }
} 
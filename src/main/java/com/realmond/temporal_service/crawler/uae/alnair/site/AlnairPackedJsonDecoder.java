package com.realmond.temporal_service.crawler.uae.alnair.site;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.alnair.decoder.PackedJsonDecoder;
import feign.FeignException;
import feign.Response;
import feign.codec.Decoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Feign decoder that transforms Alnair packed JSON array responses ("*.data")
 * to strongly typed DTOs using {@link PackedJsonDecoder}.
 */
@Slf4j
@RequiredArgsConstructor
public class AlnairPackedJsonDecoder implements Decoder {

    private final ObjectMapper objectMapper;

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        if (response == null || response.body() == null) {
            return null;
        }

        String rawJson;
        try (java.io.InputStream is = response.body().asInputStream()) {
            rawJson = new String(is.readAllBytes(), StandardCharsets.UTF_8);
        }

        if (isStringReturnType(type)) {
            return rawJson;
        }

        List<Object> packedList;
        try {
            packedList = objectMapper.readValue(rawJson, List.class);
        } catch (IOException e) {
            log.error("Failed to parse packed JSON array", e);
            throw FeignException.errorStatus("Packed JSON parse error", response);
        }

        Object decoded;
        try {
            decoded = new PackedJsonDecoder(packedList).decode();
        } catch (Exception e) {
            log.error("Failed to decode packed JSON structure", e);
            throw FeignException.errorStatus("Packed JSON decode error", response);
        }

        JavaType javaType = objectMapper.getTypeFactory().constructType(type);
        try {
            return objectMapper.convertValue(decoded, javaType);
        } catch (IllegalArgumentException ex) {
            log.error("Failed to convert decoded structure to {}", javaType, ex);
            throw FeignException.errorStatus("Conversion error", response);
        }
    }

    private boolean isStringReturnType(Type type) {
        if (type instanceof Class<?>) {
            return String.class.isAssignableFrom((Class<?>) type);
        }
        return "java.lang.String".equals(type.getTypeName());
    }
} 
package com.realmond.temporal_service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@EnableFeignClients
@EnableConfigurationProperties
@ComponentScan(excludeFilters = {
    @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
        com.realmond.temporal_service.crawler.uae.property_finder.PropertyFinderFeignConfiguration.class,
        com.realmond.temporal_service.crawler.uae.alnair.api.AlnairApiFeignConfiguration.class,
        com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictFeignConfiguration.class,
        com.realmond.temporal_service.crawler.uae.alnair.site.AlnairSiteFeignConfiguration.class,
        com.realmond.temporal_service.crawler.uae.aro.AroFeignConfiguration.class
    })
})
public class TemporalServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(TemporalServiceApplication.class, args);
	}

}

package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.alnair.model.Dictionary;
import feign.FeignException;
import feign.Request;
import feign.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.Logger;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.filter.AbstractFilterable;
import org.apache.logging.log4j.core.layout.PatternLayout;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for {@link AlnairDictHtmlDecoder} covering both the happy-path
 * extraction of the catalogs object and the failure scenario where the
 * expected <script> tag is missing.
 */
class AlnairDictHtmlDecoderTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private AlnairDictHtmlDecoder newDecoder() {
        return new AlnairDictHtmlDecoder(objectMapper);
    }

    private Response buildResponse(String body) {
        Request request = Request.create(Request.HttpMethod.GET, "https://alnair.ae/app", Collections.emptyMap(), null, StandardCharsets.UTF_8, null);
        return Response.builder()
                .status(200)
                .reason("OK")
                .request(request)
                .body(body, StandardCharsets.UTF_8)
                .headers(Collections.emptyMap())
                .build();
    }

    @Test
    @DisplayName("Decoder extracts Catalogs and compoundVillaPlot id == 240 from fixture app.html")
    void decodeCatalogsFromFixture() throws Exception {
        // Load fixture HTML
        String html = new String(this.getClass().getClassLoader().getResourceAsStream("crawler/uae/alnair/app.html").readAllBytes(), StandardCharsets.UTF_8);
        Response response = buildResponse(html);

        Dictionary dict = (Dictionary) newDecoder().decode(response, Dictionary.class);
        assertNotNull(dict, "Dictionary should not be null");
        Dictionary.Catalogs catalogs = dict.getLoaderData()
                .getAppLayout()
                .getInfo()
                .getCatalogs();
        assertNotNull(catalogs, "Catalogs should not be null");
        assertNotNull(catalogs.getCompoundVillaPlot(), "compoundVillaPlot catalog should be present");
        assertEquals(240, catalogs.getCompoundVillaPlot().getId(), "compoundVillaPlot id should equal 240");
    }

    @Test
    @DisplayName("Decoder throws FeignException and logs WARN when <script> tag missing")
    void missingScriptTagTriggersWarnAndException() {
        String htmlWithoutScript = "<html><body><h1>No dictionary here</h1></body></html>";
        Response response = buildResponse(htmlWithoutScript);

        Logger logger = (Logger) LogManager.getLogger(AlnairDictHtmlDecoder.class);
        ListCaptureAppender appender = new ListCaptureAppender();
        logger.addAppender(appender);
        logger.setAdditive(false);

        try {
            assertThrows(FeignException.class, () -> newDecoder().decode(response, Dictionary.class));
        } finally {
            logger.removeAppender(appender);
        }

        List<LogEvent> events = appender.events;
        assertFalse(events.isEmpty(), "Expected a log entry to be captured");
        LogEvent first = events.get(0);
        assertEquals(org.apache.logging.log4j.Level.WARN, first.getLevel());
        assertTrue(first.getMessage().getFormattedMessage().contains("Stream controller script not found"));
    }

    // Simple appender capturing log events in-memory
    private static class ListCaptureAppender extends AbstractAppender {
        final java.util.ArrayList<LogEvent> events = new java.util.ArrayList<>();

        protected ListCaptureAppender() {
            super("capture", null, PatternLayout.createDefaultLayout(), false, null);
            start();
        }

        @Override
        public void append(LogEvent event) {
            events.add(event.toImmutable());
        }
    }
} 
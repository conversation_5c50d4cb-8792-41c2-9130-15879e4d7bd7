package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.alnair.model.ProjectPageResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies correct deserialisation of the Alnair project-details payload using both
 * example fixtures (Golf Meadow & Olivia Gardens Residence).
 *
 * The assertions purposefully traverse the object graph deeply so that every
 * important sub-structure (brochures, galleries, payment plans, statistics, …)
 * is validated at least once.
 */
class ProjectPageResponseTest {

    private ObjectMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
    }

    /* --------------------------------------------------------------- */
    /*                        Helper methods                           */
    /* --------------------------------------------------------------- */

    private ProjectPageResponse readFixture(String resourcePath) throws Exception {
        try (InputStream in = getClass().getResourceAsStream(resourcePath)) {
            assertThat(in).as("fixture stream %s", resourcePath).isNotNull();
            return mapper.readValue(in, ProjectPageResponse.class);
        }
    }

    /* --------------------------------------------------------------- */
    /*                            TESTS                                */
    /* --------------------------------------------------------------- */

    @Nested
    @DisplayName("Golf Meadow (details.project.decoded.json)")
    class GolfMeadow {
        private ProjectPageResponse sut;

        @BeforeEach
        void init() throws Exception {
            sut = readFixture("/crawler/uae/alnair/details.project.decoded.json");
        }

        @Test
        void root_shouldDeserialize() {
            assertThat(sut).isNotNull();
            assertThat(sut.getProjectPage()).isNotNull();
            assertThat(sut.getProjectPage().getData()).isNotNull();
        }

        @Test
        void project_basicFields() {
            var project = sut.getProjectPage().getData().getProject();
            assertThat(project.getId()).isEqualTo(3843);
            assertThat(project.getTitle()).isEqualTo("Golf Meadow");
            assertThat(project.getType()).isEqualTo("project");
            assertThat(project.getCompletedAt()).isNull(); // not yet completed
            assertThat(project.getWebsite()).isEqualTo("https://properties.emaar.com/en/properties/golf-meadow-at-emaar-south/");
        }

        @Test
        void builder_block() {
            var builder = sut.getProjectPage().getData().getProject().getBuilder();
            assertThat(builder).isNotNull();
            assertThat(builder.getId()).isEqualTo(6);
            assertThat(builder.getTitle()).isEqualTo("Emaar Properties");
            assertThat(builder.getLogo()).isNotNull();
            assertThat(builder.getLogo().getSrc()).contains("alnair.ae/uploads");
        }

        @Test
        void galleries_and_photos() {
            var galleries = sut.getProjectPage().getData().getProject().getGalleries();
            assertThat(galleries).hasSize(4);

            var constructionGallery = galleries.stream()
                    .filter(g -> "project_gallery_category_construction_progress".equals(g.getType()))
                    .findFirst()
                    .orElseThrow();
            assertThat(constructionGallery.getPhotos()).hasSize(2);
            assertThat(constructionGallery.getPhotos().get(0).getSrc()).contains("gallery_photo");
        }

        @Test
        void brochures() {
            var brochures = sut.getProjectPage().getData().getProject().getBrochures();
            assertThat(brochures).singleElement()
                    .satisfies(b -> {
                        assertThat(b.getTitle()).endsWith(".pdf");
                        assertThat(b.getSize()).isGreaterThan(5_000_000);
                    });
        }

        @Test
        void catalogs_present() {
            var catalogs = sut.getProjectPage().getData().getProject().getCatalogs();
            assertThat(catalogs.getDevelopmentStage()).containsExactly(117);
            assertThat(catalogs.getProjectFacilities()).containsExactlyInAnyOrder(286, 281, 463);
            assertThat(catalogs.getProjectSalesStatus()).containsExactly(5);
        }

        @Test
        void constructions_statistics() {
            var construction = sut.getProjectPage().getData().getProject().getConstructions().get(0);
            assertThat(construction.getAddress()).contains("Building 1");
            assertThat(construction.getStatistics().getTotal().getUnitsCount()).isEqualTo(224);
            assertThat(construction.getStatistics().getUnits()).containsKey("113");
            assertThat(construction.getStatistics().getUnits().get("113").getCount()).isEqualTo(11);
        }

        @Test
        void paymentPlan() {
            var plan = sut.getProjectPage().getData().getProject().getPaymentPlans().get(0);
            assertThat(plan.getTitle()).isEqualTo("80/20 Payment Plan");
            assertThat(plan.getItems()).containsKeys("booking", "construction", "handover");
            assertThat(plan.getInfo().getOnBookingPercent()).isEqualTo(14);
            assertThat(plan.getInfo().getAdditionalPercent()).isEqualTo(4);
        }

        @Test
        void statistics_total() {
            var total = sut.getProjectPage().getData().getProject().getStatistics().getTotal();
            assertThat(total.getPriceFrom()).isEqualTo(2_738_888);
            assertThat(total.getUnitsAreaMt()).isEqualTo("4092.41");
        }
    }

    @Nested
    @DisplayName("Olivia Gardens Residence (details2.project.decoded.json)")
    class OliviaGardens {
        private ProjectPageResponse sut;

        @BeforeEach
        void init() throws Exception {
            sut = readFixture("/crawler/uae/alnair/details2.project.decoded.json");
        }

        @Test
        void project_basicFields() {
            var project = sut.getProjectPage().getData().getProject();
            assertThat(project.getId()).isEqualTo(3869);
            assertThat(project.getTitle()).isEqualTo("Olivia Gardens Residence");
            assertThat(project.getWebsite()).isNull(); // website not provided in fixture
        }

        @Test
        void builder_block() {
            var builder = sut.getProjectPage().getData().getProject().getBuilder();
            assertThat(builder.getTitle()).isEqualTo("Segrex Development");
            assertThat(builder.getAddress()).startsWith("Onyx tower");
        }

        @Test
        void galleries_counts() {
            var galleries = sut.getProjectPage().getData().getProject().getGalleries();
            assertThat(galleries).hasSize(4);
            assertThat(galleries.stream().mapToInt(g -> g.getPhotos().size()).sum()).isGreaterThan(20);
        }

        @Test
        void catalogs_badgesPresent() {
            var catalogs = sut.getProjectPage().getData().getProject().getCatalogs();
            assertThat(catalogs.getProjectBadges()).containsExactly(366);
        }

        @Test
        void paymentPlan_5050() {
            var plan = sut.getProjectPage().getData().getProject().getPaymentPlans().get(0);
            assertThat(plan.getTitle()).contains("50/50");
            assertThat(plan.getInfo().getOnHandoverPercent()).isEqualTo(50);
            assertThat(plan.getInfo().getOnConstructionPercent()).isEqualTo(30);
        }

        @Test
        void polygon_and_media() {
            var project = sut.getProjectPage().getData().getProject();
            // verify polygon contains expected number of points
            assertThat(project.getPolygon()).hasSize(5);
            assertThat(project.getPolygon().get(0)).containsExactly(55.276172, 25.224817);

            // cover & logo presence
            assertThat(project.getCover()).isNotNull();
            assertThat(project.getCover().getSrc()).contains("jpg");
            assertThat(project.getLogo()).isNotNull();
            assertThat(project.getLogo().getId()).isEqualTo(329095);
        }

        @Test
        void sales_offices_and_eoi() {
            var project = sut.getProjectPage().getData().getProject();
            assertThat(project.getSalesOffices()).isNotEmpty();
            var office = project.getSalesOffices().get(0);
            assertThat(office.getTitle()).isNotBlank();
            assertThat(office.getCommissionPercent()).isEqualTo("5.00");
            assertThat(project.getEoi()).isNotNull();
            assertThat(project.getEoi().getIsEoiReturn()).isFalse();
        }

        @Test
        void paymentPlan_items_bookingPhase() {
            var plan = sut.getProjectPage().getData().getProject().getPaymentPlans().get(0);
            var bookingItems = plan.getItems().get("booking");
            assertThat(bookingItems).isNotEmpty();
            var first = bookingItems.get(0);
            assertThat(first.getPercent()).isEqualTo(20);
            assertThat(first.getKey()).isEqualTo("payment_plan_when_down_payment");
            assertThat(first.getWhenPeriod()).isEqualTo("day");
        }

        @Test
        void media_and_sales_offices() {
            var project = sut.getProjectPage().getData().getProject();
            // cover and logo should exist
            assertThat(project.getCover()).isNotNull();
            assertThat(project.getLogo()).isNotNull();

            // sales offices block should be present but may be fewer
            assertThat(project.getSalesOffices()).isNotEmpty();
            assertThat(project.getSalesOffices().get(0).getCommissionPercent()).isEqualTo("5.00");
        }

        @Test
        void construction_polygon_and_stats() {
            var c = sut.getProjectPage().getData().getProject().getConstructions().get(0);
            assertThat(c.getPolygon()).hasSize(5);
            assertThat(c.getStatistics().getTotal().getUnitsCount()).isEqualTo(78);
        }
    }
}
